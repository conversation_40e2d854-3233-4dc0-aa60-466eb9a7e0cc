#!/usr/bin/env python3
"""
测试重构后的HandModel是否与原始版本功能一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from utils.hand_model import HandModel
from utils.hand_types import HandModelType

# 检查CUDA是否可用，如果可用则使用CUDA，否则使用CPU
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {DEVICE}")

def test_basic_initialization():
    """测试基本初始化"""
    print("Testing basic initialization...")

    # 测试基本初始化
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP,
        n_surface_points=100,
        rot_type="quat",
        device=DEVICE
    )
    
    # 检查基本属性
    assert hasattr(hand_model, 'chain')
    assert hasattr(hand_model, 'mesh')
    assert hasattr(hand_model, 'areas')
    assert hasattr(hand_model, 'n_dofs')
    assert hasattr(hand_model, 'joints_lower')
    assert hasattr(hand_model, 'joints_upper')
    
    # 检查子模块
    assert hasattr(hand_model, 'physics')
    assert hasattr(hand_model, 'visualizer')
    assert hasattr(hand_model, 'loader')
    
    print("✓ Basic initialization test passed")

def test_set_parameters():
    """测试set_parameters方法"""
    print("Testing set_parameters...")

    hand_model = HandModel(device=DEVICE)
    
    # 创建测试数据
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4  # translation + joints + quaternion
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    
    # 测试设置参数
    hand_model.set_parameters(hand_pose)
    
    # 检查状态变量
    assert hand_model.hand_pose is not None
    assert hand_model.global_translation is not None
    assert hand_model.global_rotation is not None
    assert hand_model.current_status is not None
    
    print("✓ set_parameters test passed")

def test_multi_grasp_format():
    """测试多抓取格式支持"""
    print("Testing multi-grasp format...")

    hand_model = HandModel(device=DEVICE)

    # 创建多抓取格式的测试数据
    batch_size = 2
    num_grasps = 3
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, num_grasps, pose_dim, device=DEVICE)
    
    # 测试设置参数
    hand_model.set_parameters(hand_pose)
    
    # 检查多抓取格式的状态
    assert hand_model.is_multi_grasp == True
    assert hand_model.batch_size_original == batch_size
    assert hand_model.num_grasps == num_grasps
    assert hand_model.global_translation.shape == (batch_size, num_grasps, 3)
    assert hand_model.global_rotation.shape == (batch_size, num_grasps, 3, 3)
    
    print("✓ Multi-grasp format test passed")

def test_physics_methods():
    """测试物理计算方法"""
    print("Testing physics methods...")

    hand_model = HandModel(device=DEVICE)

    # 设置手部姿态
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(hand_pose)

    # 测试各种物理计算方法
    try:
        # 测试自碰撞能量
        spen_energy = hand_model.cal_self_penetration_energy()
        assert spen_energy.shape == (batch_size,)

        # 测试关节限位能量
        joint_energy = hand_model.cal_joint_limit_energy()
        assert joint_energy.shape == (batch_size,)

        # 测试距离计算
        test_points = torch.randn(batch_size, 100, 3, device=DEVICE)
        distances = hand_model.cal_distance(test_points)
        assert distances.shape == (batch_size, 100)
        
        print("✓ Physics methods test passed")
        
    except Exception as e:
        print(f"✗ Physics methods test failed: {e}")
        raise

def test_visualization_methods():
    """测试可视化方法"""
    print("Testing visualization methods...")

    hand_model = HandModel(device=DEVICE, n_surface_points=50)

    # 设置手部姿态
    batch_size = 1
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    hand_model.set_parameters(hand_pose)
    
    try:
        # 测试获取表面点
        surface_points = hand_model.get_surface_points()
        assert surface_points.shape[0] == batch_size
        assert surface_points.shape[2] == 3
        
        # 测试获取接触候选点
        contact_candidates = hand_model.get_contact_candidates()
        assert contact_candidates.shape[0] == batch_size
        assert contact_candidates.shape[2] == 3
        
        # 测试可视化数据获取
        plotly_data = hand_model.get_plotly_data(0)
        assert isinstance(plotly_data, list)
        
        trimesh_data = hand_model.get_trimesh_data(0)
        assert hasattr(trimesh_data, 'vertices')
        
        print("✓ Visualization methods test passed")
        
    except Exception as e:
        print(f"✗ Visualization methods test failed: {e}")
        raise

def test_call_method():
    """测试__call__方法"""
    print("Testing __call__ method...")

    hand_model = HandModel(device=DEVICE, n_surface_points=50)

    # 创建测试数据
    batch_size = 2
    pose_dim = 3 + hand_model.n_dofs + 4
    hand_pose = torch.randn(batch_size, pose_dim, device=DEVICE)
    scene_pc = torch.randn(batch_size, 100, 4, device=DEVICE)  # 包含mask维度
    
    try:
        # 测试__call__方法
        result = hand_model(
            hand_pose,
            scene_pc=scene_pc,
            with_surface_points=True,
            with_penetration_keypoints=True,
            with_meshes=True,
            with_fingertip_keypoints=True
        )
        
        # 检查返回结果
        assert 'surface_points' in result
        assert 'penetration_keypoints' in result
        assert 'vertices' in result
        assert 'faces' in result
        assert 'fingertip_keypoints' in result
        
        print("✓ __call__ method test passed")
        
    except Exception as e:
        print(f"✗ __call__ method test failed: {e}")
        raise

def main():
    """运行所有测试"""
    print("Starting HandModel refactor validation tests...\n")
    
    try:
        test_basic_initialization()
        test_set_parameters()
        test_multi_grasp_format()
        test_physics_methods()
        test_visualization_methods()
        test_call_method()
        
        print("\n🎉 All tests passed! The refactor appears to be successful.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
