import json
import os
import pathlib
from collections import defaultdict
from enum import Enum, auto
from typing import Optional, Union, Dict, List, Tuple

import numpy as np
import plotly.graph_objects as go
import pytorch_kinematics as pk
import torch
import trimesh as tm
from torchsdf import index_vertices_by_faces, compute_sdf

# Define slice constants for pose components
TRANSLATION_SLICE = slice(0, 3)
QPOS_SLICE = slice(3, 19)
ROTATION_SLICE = slice(19, None)

# Attempt to import PyTorch3D for specific functionalities
try:
    from pytorch3d.ops import knn_points
    from pytorch3d.structures import Meshes as PyTorch3DMeshes
    from pytorch3d.ops import sample_points_from_meshes as pytorch3d_sample_points_from_meshes
    from pytorch3d.ops import sample_farthest_points as pytorch3d_sample_farthest_points
    _PYTORCH3D_AVAILABLE = True
except ImportError:
    _PYTORCH3D_AVAILABLE = False
    # Define dummy functions or raise errors if PyTorch3D is critical for some operations
    # For now, methods requiring these will check _PYTORCH3D_AVAILABLE

from utils.leap_hand_info import (
    LEAP_HAND_CONTACT_POINTS_PATH,
    LEAP_HAND_DEFAULT_JOINT_ANGLES,
    LEAP_HAND_DEFAULT_ORIENTATION,
    LEAP_HAND_FINGERTIP_KEYWORDS,
    LEAP_HAND_FINGERTIP_NAMES,
    LEAP_HAND_JOINT_NAMES,
    LEAP_HAND_NUM_FINGERS,
    LEAP_HAND_NUM_JOINTS,
    LEAP_HAND_PENETRATION_POINTS_PATH,
    LEAP_HAND_URDF_PATH,
)
from utils.rot6d import (
    robust_compute_rotation_matrix_from_ortho6d,
)
from utils.point_utils import transform_points
from utils.hand_loader import HandLoader
from utils.hand_physics import HandPhysics
from utils.hand_visualizer import HandVisualizer
from utils.hand_types import HandModelType


SELF_PENETRATION_POINT_RADIUS = 0.01


class HandModel:
    def __init__(
        self,
        hand_model_type: HandModelType = HandModelType.LEAP,
        n_surface_points: int = 0,
        rot_type: str = "quat",
        device: Union[str, torch.device] = "cpu",
    ) -> None:
        """
        Create a Hand Model for a MJCF robot

        Parameters
        ----------
        hand_model_type: HandModelType
            type of hand to use
        n_surface_points: int
            number of surface points to sample
        device: str | torch.Device
            device for torch tensors
        """
        self.hand_model_type = hand_model_type
        self.n_surface_points = n_surface_points
        self.rot_type = rot_type
        self.device = device

        # ------------------ 使用 HandLoader 加载静态资源 ------------------
        self.loader = HandLoader(
            urdf_path=self.urdf_path,
            contact_points_path=self.contact_points_path,
            penetration_points_path=self.penetration_points_path,
            n_surface_points=n_surface_points,
            device=device,
            joint_names=self.joint_names,
        )
        loaded_data = self.loader.load()
        for key, value in loaded_data.items():
            setattr(self, key, value)

        # ------------------ 初始化子模块 ------------------
        self.physics = HandPhysics(self)
        # HandVisualizer 将在需要时延迟初始化
        self._visualizer = None

        # indexing
        self.link_name_to_link_index = {
            link_name: idx for idx, link_name in enumerate(self.mesh)
        }
        self.link_name_to_contact_candidates = {
            link_name: self.mesh[link_name]["contact_candidates"]
            for link_name in self.mesh
        }
        contact_candidates = [
            self.link_name_to_contact_candidates[link_name] for link_name in self.mesh
        ]
        self.global_index_to_link_index = sum(
            [
                [i] * len(contact_candidates)
                for i, contact_candidates in enumerate(contact_candidates)
            ],
            [],
        )
        self.link_index_to_global_indices = defaultdict(list)
        for global_idx, link_idx in enumerate(self.global_index_to_link_index):
            self.link_index_to_global_indices[link_idx].append(global_idx)

        self.contact_candidates = torch.cat(contact_candidates, dim=0)
        self.global_index_to_link_index = torch.tensor(
            self.global_index_to_link_index, dtype=torch.long, device=device
        )
        self.n_contact_candidates = self.contact_candidates.shape[0]

        self.penetration_keypoints = [
            self.mesh[link_name]["penetration_keypoints"] for link_name in self.mesh
        ]
        self.global_index_to_link_index_penetration = sum(
            [
                [i] * len(penetration_keypoints)
                for i, penetration_keypoints in enumerate(self.penetration_keypoints)
            ],
            [],
        )
        self.penetration_keypoints = torch.cat(self.penetration_keypoints, dim=0)
        self.global_index_to_link_index_penetration = torch.tensor(
            self.global_index_to_link_index_penetration, dtype=torch.long, device=device
        )
        self.n_keypoints = self.penetration_keypoints.shape[0]

        # parameters
        self.hand_pose = None
        self.contact_point_indices = None
        self.global_translation = None
        self.global_rotation = None
        self.current_status = None
        self.contact_points = None

    @property
    def visualizer(self):
        """延迟初始化可视化器"""
        if self._visualizer is None:
            self._visualizer = HandVisualizer(self)
        return self._visualizer

    def _ensure_device_consistency(self, *tensors: torch.Tensor):
        """
        Ensures all input tensors are on the correct device (self.device).

        Args:
            *tensors: Variable number of tensors to check and convert

        Returns:
            Single tensor if one input, tuple of tensors if multiple inputs
        """
        converted_tensors = []
        for tensor in tensors:
            if tensor is not None and tensor.device != self.device:
                converted_tensors.append(tensor.to(self.device))
            else:
                converted_tensors.append(tensor)

        # Return single tensor if only one input, tuple otherwise
        if len(converted_tensors) == 1:
            return converted_tensors[0]
        else:
            return tuple(converted_tensors)

    # ------------------------------------------------------------------
    # 以下三个方法在之前的重构过程中被误删，为保证现有功能不受影响，
    # 现完整恢复其实现。后续将逐步迁移到 HandLoader / HandPhysics。
    # ------------------------------------------------------------------

    def sample_contact_points(
        self, total_batch_size: int, n_contacts_per_finger: int
    ) -> torch.Tensor:
        # Ensure that each finger gets sampled at least once
        # Goal: Output (B, n_fingers * n_contacts_per_finger) torch.LongTensor of sampled contact point indices
        # Each contact point is represented by a global index
        # Each contact point is sampled from a link
        # For each finger:
        #    Get the link indices that contain the finger keyword
        #    Get the possible contact point indices from these link indices
        #    Sample from these contact point indices

        fingertip_keywords = self.fingertip_keywords

        # Get link indices that contain the finger keyword
        finger_possible_link_idxs_list = [
            [
                link_idx
                for link_name, link_idx in self.link_name_to_link_index.items()
                if finger_keyword in link_name
            ]
            for finger_keyword in fingertip_keywords
        ]

        # Get the possible contact point indices from these link indices
        finger_possible_contact_point_idxs_list = [
            sum(
                [self.link_index_to_global_indices[link_idx] for link_idx in link_idxs],
                [],
            )
            for link_idxs in finger_possible_link_idxs_list
        ]

        # Sample from these contact point indices
        sampled_contact_point_idxs_list = []
        for (
            finger_possible_contact_point_idxs
        ) in finger_possible_contact_point_idxs_list:
            sampled_idxs = torch.randint(
                len(finger_possible_contact_point_idxs),
                size=[total_batch_size, n_contacts_per_finger],
                device=self.device,
            )
            sampled_contact_point_idxs = torch.tensor(
                finger_possible_contact_point_idxs, device=self.device, dtype=torch.long
            )[sampled_idxs]
            sampled_contact_point_idxs_list.append(sampled_contact_point_idxs)
        sampled_contact_point_idxs_list = torch.cat(
            sampled_contact_point_idxs_list, dim=1
        )

        assert sampled_contact_point_idxs_list.shape == (
            total_batch_size,
            len(fingertip_keywords) * n_contacts_per_finger,
        )

        return sampled_contact_point_idxs_list
    def set_parameters(
        self,
        hand_pose: torch.Tensor,
        contact_point_indices: Optional[torch.Tensor] = None,
    ) -> None:
        """
        Set translation, joint angles, rotation, and contact points of the hand model.
        The representation of the rotation is determined by `self.rot_type`.
        The hand_pose tensor is structured as: translation (3D), joint angles (self.n_dofs D),
        and rotation parameters (rot_dim D), in that specific order.

        Supports both single and multi-grasp formats:
        - Single grasp: [B, pose_dim]
        - Multi grasp: [B, num_grasps, pose_dim]

        Parameters
        ----------
        hand_pose: (B, 3 + `self.n_dofs` + rot_dim) or (B, num_grasps, 3 + `self.n_dofs` + rot_dim) torch.FloatTensor
            A concatenated tensor containing translation, joint angles, and rotation parameters.
            The order is: translation, then joint angles, then rotation parameters.
        contact_point_indices: (B, `n_contact`) [Optional] torch.LongTensor
            Indices of contact candidates.
        """
        # 任务1.1: 输入格式检测
        if hand_pose.dim() == 1:
            hand_pose = hand_pose.unsqueeze(0)

        # 检测输入格式并记录元数据
        if hand_pose.dim() == 2:
            # 单抓取格式: [B, pose_dim]
            self.is_multi_grasp = False
            self.batch_size_original = hand_pose.shape[0]
            self.num_grasps = 1
            hand_pose_for_processing = hand_pose
        elif hand_pose.dim() == 3:
            # 多抓取格式: [B, num_grasps, pose_dim]
            self.is_multi_grasp = True
            self.batch_size_original = hand_pose.shape[0]
            self.num_grasps = hand_pose.shape[1]
            # 任务1.2: 张量展平处理 - 将3D输入展平为2D以复用现有逻辑
            hand_pose_for_processing = hand_pose.view(-1, hand_pose.shape[-1])
        else:
            raise ValueError(f"hand_pose must be 2D or 3D tensor, got {hand_pose.dim()}D tensor with shape {hand_pose.shape}")

        assert hand_pose_for_processing.dim() == 2, "Internal error: hand_pose_for_processing must be 2D after processing."

        # Determine rotation parameter dimension based on self.rot_type
        if self.rot_type == 'r6d':
            rot_dim = 6
        elif self.rot_type == 'quat':
            rot_dim = 4
        elif self.rot_type == 'axis':  # Axis-angle
            rot_dim = 3
        elif self.rot_type == 'euler': # Euler angles
            rot_dim = 3
        else:
            raise ValueError(f"Unsupported rotation type: {self.rot_type}")

        expected_dim = 3 + self.n_dofs + rot_dim
        assert hand_pose_for_processing.shape[1] == expected_dim, \
            f"Hand pose shape error (rot_type: '{self.rot_type}'). " \
            f"Expected last dimension to be {expected_dim} (3 trans + {self.n_dofs} DoFs + {rot_dim} rot), " \
            f"but got {hand_pose_for_processing.shape[1]}."

        # 确保输入tensor在正确的设备上
        hand_pose_for_processing = self._ensure_device_consistency(hand_pose_for_processing)

        # 保存原始输入和处理后的张量
        self.hand_pose_original = hand_pose  # 保存原始输入格式
        self.hand_pose = hand_pose_for_processing  # 用于后续处理的2D张量
        if self.hand_pose.requires_grad:
            self.hand_pose.retain_grad()

        # Decompose hand_pose: translation | joint_angles | rotation_parameters
        global_translation_flat = self.hand_pose[:, TRANSLATION_SLICE]

        joint_angles = self.hand_pose[:, QPOS_SLICE] # Use local variable for FK

        # 确保joint_angles在正确的设备上（用于forward kinematics）
        joint_angles = self._ensure_device_consistency(joint_angles)

        rotation_params = self.hand_pose[:, ROTATION_SLICE]

        # Convert rotation parameters to rotation matrix
        if self.rot_type == 'r6d':
            from pytorch3d.transforms import rotation_6d_to_matrix
            global_rotation_flat = rotation_6d_to_matrix(rotation_params)
        elif self.rot_type == 'quat':
            from pytorch3d.transforms import quaternion_to_matrix
            global_rotation_flat = quaternion_to_matrix(rotation_params)
        elif self.rot_type == 'axis':
            from pytorch3d.transforms import axis_angle_to_matrix
            global_rotation_flat = axis_angle_to_matrix(rotation_params)
        elif self.rot_type == 'euler':
            from pytorch3d.transforms import euler_angles_to_matrix
            # Assuming "XYZ" convention for Euler angles
            global_rotation_flat = euler_angles_to_matrix(rotation_params, convention="XYZ")

        # 任务1.3: 状态变量重塑 - 将处理后的状态变量重塑回多抓取格式
        if self.is_multi_grasp:
            # 重塑为多抓取格式
            self.global_translation = global_translation_flat.view(self.batch_size_original, self.num_grasps, 3)
            self.global_rotation = global_rotation_flat.view(self.batch_size_original, self.num_grasps, 3, 3)
        else:
            # 保持单抓取格式
            self.global_translation = global_translation_flat
            self.global_rotation = global_rotation_flat

        # Forward kinematics for joint angles
        self.current_status = self.chain.forward_kinematics(joint_angles)
        
        # Process contact points
        if contact_point_indices is not None:
            self.contact_point_indices = contact_point_indices
            batch_size, n_contact = contact_point_indices.shape
            
            # Get contact points in local link coordinates
            # Shape: (B, n_contact, 3)
            local_contact_points = self.contact_candidates[self.contact_point_indices]
            
            # Get link index for each contact point
            # Shape: (B, n_contact)
            link_indices = self.global_index_to_link_index[self.contact_point_indices]
            
            # Initialize tensor for link transforms (from link local to hand base frame)
            # Shape: (B, n_contact, 4, 4)
            contact_link_transforms = torch.zeros(
                batch_size, n_contact, 4, 4, dtype=torch.float, device=self.device
            )
            
            for link_name in self.mesh:
                link_idx_val = self.link_name_to_link_index[link_name]
                # Create a mask for contact points belonging to the current link
                mask = (link_indices == link_idx_val) # Shape: (B, n_contact)
                
                if not mask.any(): # Optimization: skip if no contact points on this link
                    continue

                # Get the FK transform matrix for the current link (T_link_to_base)
                # Shape: (B, 4, 4)
                current_link_fk_matrix = self.current_status[link_name].get_matrix()
                
                # Expand and assign the link's transform to the corresponding entries in contact_link_transforms
                # Unsqueeze to (B, 1, 4, 4), expand to (B, n_contact, 4, 4)
                expanded_fk_matrix = current_link_fk_matrix.unsqueeze(1).expand(batch_size, n_contact, 4, 4)
                contact_link_transforms[mask] = expanded_fk_matrix[mask]
            
            # Convert local contact points to homogeneous coordinates
            # Shape: (B, n_contact, 4)
            contact_points_homog = torch.cat(
                [
                    local_contact_points, # (B, n_contact, 3)
                    torch.ones(batch_size, n_contact, 1, dtype=torch.float, device=self.device),
                ],
                dim=2,
            )
            
            # Transform contact points from local link frames to the hand's base frame
            # P_base = T_link_to_base @ P_local_homog
            # contact_points_homog.unsqueeze(3) changes shape to (B, n_contact, 4, 1) for matmul
            # Resulting shape after matmul and slicing: (B, n_contact, 3)
            contact_points_in_hand_base_frame = (contact_link_transforms @ contact_points_homog.unsqueeze(3))[:, :, :3, 0]
            
            # Transform contact points from the hand's base frame to the world frame
            # P_world = P_base @ R_global.T + T_global (assuming P_base are row vectors)
            # 处理多抓取格式的global_rotation和global_translation
            if self.is_multi_grasp:
                # global_rotation: (B_orig, num_grasps, 3, 3), global_translation: (B_orig, num_grasps, 3)
                # 需要展平以匹配contact_points_in_hand_base_frame的批次维度
                global_rotation_flat = self.global_rotation.view(-1, 3, 3)  # (B*num_grasps, 3, 3)
                global_translation_flat = self.global_translation.view(-1, 3)  # (B*num_grasps, 3)
                self.contact_points = contact_points_in_hand_base_frame @ global_rotation_flat.transpose(
                    1, 2
                ) + global_translation_flat.unsqueeze(1)
            else:
                # 单抓取格式：self.global_rotation is (B, 3, 3), self.global_translation is (B, 3)
                self.contact_points = contact_points_in_hand_base_frame @ self.global_rotation.transpose(
                    1, 2
                ) + self.global_translation.unsqueeze(1)
        else:
            self.contact_point_indices = None
            self.contact_points = None

    def decompose_hand_pose(self, hand_pose: torch.Tensor, rot_type: Optional[str] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Decomposes the hand_pose tensor into global translation, global rotation matrix, and joint angles.

        Parameters
        ----------
        hand_pose: (B, 3 + self.n_dofs + rot_dim) torch.FloatTensor
            A concatenated tensor containing translation, joint angles, and rotation parameters.
            The order is: translation, then joint angles, then rotation parameters.
        rot_type: str, optional
            The type of rotation representation used in hand_pose. 
            If None, self.rot_type is used. Supported types: 'r6d', 'quat', 'axis', 'euler'.

        Returns
        -------
        global_translation: (B, 3) torch.FloatTensor
        global_rotation: (B, 3, 3) torch.FloatTensor
        qpos: (B, self.n_dofs) torch.FloatTensor
        """
        if hand_pose.dim() == 1:
            hand_pose = hand_pose.unsqueeze(0)
        assert hand_pose.dim() == 2, "hand_pose must be a 1D or 2D tensor for decomposition."

        current_rot_type = rot_type if rot_type is not None else self.rot_type

        # Determine rotation parameter dimension based on current_rot_type
        if current_rot_type == 'r6d':
            rot_dim = 6
        elif current_rot_type == 'quat':
            rot_dim = 4
        elif current_rot_type == 'axis':  # Axis-angle
            rot_dim = 3
        elif current_rot_type == 'euler': # Euler angles
            rot_dim = 3
        else:
            raise ValueError(f"Unsupported rotation type for decomposition: {current_rot_type}")

        expected_dim = 3 + self.n_dofs + rot_dim
        assert hand_pose.shape[1] == expected_dim, \
            f"Hand pose shape error for decomposition (rot_type: '{current_rot_type}'). " \
            f"Expected last dimension to be {expected_dim} (3 trans + {self.n_dofs} DoFs + {rot_dim} rot), " \
            f"but got {hand_pose.shape[1]}."

        global_translation = hand_pose[:, TRANSLATION_SLICE]
        
        qpos = hand_pose[:, QPOS_SLICE]
        
        rotation_params = hand_pose[:, ROTATION_SLICE]

        # Convert rotation parameters to rotation matrix
        global_rotation: torch.Tensor
        if current_rot_type == 'r6d':
            from pytorch3d.transforms import rotation_6d_to_matrix
            global_rotation = rotation_6d_to_matrix(rotation_params)
        elif current_rot_type == 'quat':
            from pytorch3d.transforms import quaternion_to_matrix
            global_rotation = quaternion_to_matrix(rotation_params)
        elif current_rot_type == 'axis':
            from pytorch3d.transforms import axis_angle_to_matrix
            global_rotation = axis_angle_to_matrix(rotation_params)
        elif current_rot_type == 'euler':
            from pytorch3d.transforms import euler_angles_to_matrix
            # Assuming "XYZ" convention for Euler angles, consistent with set_parameters
            global_rotation = euler_angles_to_matrix(rotation_params, convention="XYZ")
        
        return global_translation, global_rotation, qpos

    def cal_distance(self, x: torch.Tensor) -> torch.Tensor:
        """
        Calculates the distance from each point in `x` to the hand model.
        This is now a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_distance(x)

    def _compute_object_penetration(self, x: torch.Tensor) -> torch.Tensor:
        """
        Calculates the signed distance from each point in object point cloud `x` to the hand model.
        This is now a proxy to the HandPhysics implementation.
        """
        return self.physics.compute_object_penetration(x)

    def cal_self_penetration_energy(self) -> torch.Tensor:
        """
        Calculate self penetration energy.
        This is a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_self_penetration_energy()

    def cal_joint_limit_energy(self) -> torch.Tensor:
        """
        Calculate joint limit energy.
        This is a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_joint_limit_energy()

    def cal_finger_finger_distance_energy(self) -> torch.Tensor:
        """
        Calculate finger-finger distance energy.
        This is a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_finger_finger_distance_energy()

    def cal_finger_palm_distance_energy(self) -> torch.Tensor:
        """
        Calculate finger-palm distance energy.
        This is a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_finger_palm_distance_energy()

    def cal_table_penetration(
        self, table_pos: torch.Tensor, table_normal: torch.Tensor
    ) -> torch.Tensor:
        """
        Calculate table penetration energy.
        This is a proxy to the HandPhysics implementation.
        """
        return self.physics.cal_table_penetration(table_pos, table_normal)

    def get_surface_points(self) -> torch.Tensor:
        points = []
        # 对于多抓取格式，使用展平后的批次大小
        if self.is_multi_grasp:
            batch_size = self.batch_size_original * self.num_grasps
        else:
            batch_size = self.global_translation.shape[0]
        device = self.device

        for link_name in self.mesh:
            link_local_surface_points = self.mesh[link_name]["surface_points"]  # Shape (N_k, 3)
            n_points_for_this_link = link_local_surface_points.shape[0]

            if n_points_for_this_link == 0:
                points.append(torch.empty((batch_size, 0, 3), dtype=link_local_surface_points.dtype, device=device))
                continue

            transformed_link_points = self.current_status[link_name].transform_points(link_local_surface_points)
            original_shape_from_transform = transformed_link_points.shape # For logging if needed

            # Ensure the transformed_link_points has the correct target shape: (batch_size, n_points_for_this_link, 3)
            if transformed_link_points.ndim == 3:
                # Case 1: Output is (1, N_k, 3) but batch_size > 1. Needs expansion.
                if transformed_link_points.shape[0] == 1 and batch_size > 1 and \
                   transformed_link_points.shape[1] == n_points_for_this_link and \
                   transformed_link_points.shape[2] == 3:
                    transformed_link_points = transformed_link_points.expand(batch_size, n_points_for_this_link, 3)
                # Else, if ndim is 3, assume it's correctly (B, N_k, 3) or the warning below will catch discrepancies.
            elif transformed_link_points.ndim == 2:
                # Case 2a: Output is (N_k, 3). This is the main problematic case from logs.
                # This occurs if transform_points returns an unbatched result.
                if transformed_link_points.shape[0] == n_points_for_this_link and \
                   transformed_link_points.shape[1] == 3:
                    # Regardless of batch_size (1 or >1), unsqueeze to (1, N_k, 3) then expand to (B, N_k, 3).
                    # If B=1, expand(1,N_k,3) is fine.
                    transformed_link_points = transformed_link_points.unsqueeze(0).expand(batch_size, n_points_for_this_link, 3)
                # Case 2b: Output is (B, 3). This implies N_k was 1 and that dimension was squeezed.
                elif transformed_link_points.shape[0] == batch_size and \
                     transformed_link_points.shape[1] == 3 and \
                     n_points_for_this_link == 1:
                    transformed_link_points = transformed_link_points.unsqueeze(1) # Shape: (batch_size, 1, 3)
            # Else (ndim not 2 or 3, or 2D/3D shape doesn't match above patterns):
            # The shape is unexpected. The warning below will trigger.

            expected_shape = (batch_size, n_points_for_this_link, 3)
            if transformed_link_points.shape != expected_shape:
                print(f"Warning: Shape mismatch for link '{link_name}' AFTER adjustments. \nOriginal output from transform_points was {original_shape_from_transform}. \nGot {transformed_link_points.shape}, expected {expected_shape}.")

            points.append(transformed_link_points)

        # Debug print (as in original code, useful for verifying shapes before concatenation)
        # for i, p in enumerate(points): # Optional: uncomment for verbose debugging
        #     print(f"Shape of points[{i}] for link {list(self.mesh.keys())[i]}: {p.shape}")

        try:
            final_points = torch.cat(points, dim=-2).to(device)
        except RuntimeError as e:
            error_msg = f"Error during torch.cat in get_surface_points: {e}"
            shape_info = "Tensor shapes collected:"
            for i, p in enumerate(points):
                shape_info += f"\n  points[{i}] (link: {list(self.mesh.keys())[i]}): shape {p.shape}, ndim {p.ndim}"

            # Log detailed error information
            print(f"WARNING: {error_msg}")
            print(shape_info)
            print("Returning empty tensor as fallback. Consider checking mesh configurations.")

            # Return empty tensor as fallback, but preserve error context for debugging
            return torch.empty((batch_size, 0, 3), dtype=self.global_translation.dtype, device=device)
        
        # 处理多抓取格式的global_rotation和global_translation
        if self.is_multi_grasp:
            # global_rotation: (B_orig, num_grasps, 3, 3), global_translation: (B_orig, num_grasps, 3)
            # 需要展平以匹配final_points的批次维度
            global_rotation_flat = self.global_rotation.view(-1, 3, 3)  # (B*num_grasps, 3, 3)
            global_translation_flat = self.global_translation.view(-1, 3)  # (B*num_grasps, 3)
            final_points = final_points @ global_rotation_flat.transpose(
                1, 2
            ) + global_translation_flat.unsqueeze(1)
        else:
            # 单抓取格式
            final_points = final_points @ self.global_rotation.transpose(
                1, 2
            ) + self.global_translation.unsqueeze(1)
        return final_points

    def get_contact_candidates(self) -> torch.Tensor:
        """
        Get all contact candidates

        Returns
        -------
        points: (N, `n_contact_candidates`, 3) torch.Tensor
        """
        points = []
        # 对于多抓取格式，使用展平后的批次大小
        if self.is_multi_grasp:
            batch_size = self.batch_size_original * self.num_grasps
        else:
            batch_size = self.global_translation.shape[0]

        for link_name in self.mesh:
            n_contact_candidates = self.mesh[link_name]["contact_candidates"].shape[0]
            points.append(
                self.current_status[link_name].transform_points(
                    self.mesh[link_name]["contact_candidates"]
                )
            )
            # Expand points if batch_size > 1 and the transformed points don't match expected batch size
            if batch_size > 1 and batch_size != points[-1].shape[0]:
                points[-1] = points[-1].expand(batch_size, n_contact_candidates, 3)
        points = torch.cat(points, dim=-2).to(self.device)

        return self._transform_points_to_world(points)

    def get_penetration_keypoints(self) -> torch.Tensor:
        """
        Get penetration keypoints

        Returns
        -------
        points: (N, `n_keypoints`, 3) torch.Tensor
        """
        points = []
        # 对于多抓取格式，使用展平后的批次大小
        if self.is_multi_grasp:
            batch_size = self.batch_size_original * self.num_grasps
        else:
            batch_size = self.global_translation.shape[0]

        for link_name in self.mesh:
            n_keypoints = self.mesh[link_name]["penetration_keypoints"].shape[0]
            points.append(
                self.current_status[link_name].transform_points(
                    self.mesh[link_name]["penetration_keypoints"]
                )
            )
            # Expand points if batch_size > 1 and the transformed points don't match expected batch size
            if batch_size > 1 and batch_size != points[-1].shape[0]:
                points[-1] = points[-1].expand(batch_size, n_keypoints, 3)
        points = torch.cat(points, dim=-2).to(self.device)

        return self._transform_points_to_world(points)

    def get_plotly_data(
        self,
        i: int,
        opacity: float = 0.5,
        color: str = "lightblue",
        with_contact_points: bool = False,
        with_contact_candidates: bool = False,
        with_surface_points: bool = False,
        with_penetration_keypoints: bool = False,
        pose: Optional[np.ndarray] = None,
        visual: bool = False,
    ) -> list:
        """
        Get visualization data for plotly.graph_objects.
        This is a proxy to the HandVisualizer implementation.
        """
        return self.visualizer.get_plotly_data(
            i=i,
            opacity=opacity,
            color=color,
            with_contact_points=with_contact_points,
            with_contact_candidates=with_contact_candidates,
            with_surface_points=with_surface_points,
            with_penetration_keypoints=with_penetration_keypoints,
            pose=pose,
            visual=visual,
        )

    def get_trimesh_data(self, i: int) -> tm.Trimesh:
        """
        Get full mesh.
        This is a proxy to the HandVisualizer implementation.
        """
        return self.visualizer.get_trimesh_data(i)

    @property
    def n_fingers(self) -> int:
        return LEAP_HAND_NUM_FINGERS

    @property
    def batch_size(self) -> int:
        if self.hand_pose is None:
            raise ValueError("Hand pose is not set")
        # 对于多抓取格式，返回原始批次大小而不是展平后的大小
        if hasattr(self, 'is_multi_grasp') and self.is_multi_grasp:
            return self.batch_size_original
        return self.hand_pose.shape[0]

    @property
    def num_fingers(self) -> int:
        return self.n_fingers

    @property
    def fingertip_keywords(self) -> list:
        return LEAP_HAND_FINGERTIP_KEYWORDS

    @property
    def fingertip_names(self) -> list:
        return LEAP_HAND_FINGERTIP_NAMES

    @property
    def joint_names(self) -> list:
        return LEAP_HAND_JOINT_NAMES

    @property
    def num_joints(self) -> int:
        num_joints = LEAP_HAND_NUM_JOINTS
        assert num_joints == self.n_dofs, f"{num_joints} != {self.n_dofs}"
        return num_joints

    @property
    def default_joint_angles(self) -> torch.Tensor:
        return LEAP_HAND_DEFAULT_JOINT_ANGLES

    @property
    def default_orientation(self) -> torch.Tensor:
        return LEAP_HAND_DEFAULT_ORIENTATION

    @property
    def urdf_path(self) -> pathlib.Path:
        return LEAP_HAND_URDF_PATH

    @property
    def contact_points_path(self) -> pathlib.Path:
        return LEAP_HAND_CONTACT_POINTS_PATH

    @property
    def penetration_points_path(self) -> pathlib.Path:
        return LEAP_HAND_PENETRATION_POINTS_PATH
    
    def _transform_points_to_world(self, points_in_base_frame: torch.Tensor) -> torch.Tensor:
        """
        Transforms points from the hand's base frame to the world frame,
        handling both single and multi-grasp formats.

        Args:
            points_in_base_frame: (B_flat, N, 3) tensor of points.

        Returns:
            (B_flat, N, 3) tensor of points in world frame.
        """
        if self.is_multi_grasp:
            # Flatten global rotation and translation to match the points' batch dimension
            global_rotation_flat = self.global_rotation.view(-1, 3, 3)
            global_translation_flat = self.global_translation.view(-1, 3)
            
            points_world = points_in_base_frame @ global_rotation_flat.transpose(
                1, 2
            ) + global_translation_flat.unsqueeze(1)
        else:
            # Standard transformation for single-grasp format
            points_world = points_in_base_frame @ self.global_rotation.transpose(
                1, 2
            ) + self.global_translation.unsqueeze(1)
        return points_world

    def __call__(self, hand_pose: torch.Tensor,
                 scene_pc: Optional[torch.Tensor] = None,
                 # plane_parameters: Optional[torch.Tensor] = None,
                 with_meshes: bool = False,
                 with_surface_points: bool = False,
                 with_contact_candidates: bool = False,
                 with_penetration_keypoints: bool = False,
                 with_penetration: bool = False,
                 with_distance: bool = False, # ADDED
                 with_fingertip_keypoints: bool = False
                 ) -> Dict[str, torch.Tensor]:
        """
        Main interface to get various hand model information based on hand_pose.

        Supports both single and multi-grasp formats:
        - Single grasp: [B, pose_dim]
        - Multi grasp: [B, num_grasps, pose_dim]

        Args:
            hand_pose: (B, 3+rot_dim+n_dofs) or (B, num_grasps, 3+rot_dim+n_dofs) torch.FloatTensor
                translation, rotation parameters, and joint angles.
            scene_pc: (B, N_points, 4) or (N_points, 4) [Optional] torch.FloatTensor
                Scene point cloud, last dimension is (x, y, z, mask).
                For multi-grasp format, will be expanded to match the flattened batch dimension.
            with_meshes: If True, returns 'vertices' and 'faces'.
            with_surface_points: If True, returns 'surface_points'.
            with_contact_candidates: If True and scene_pc is provided, returns 'contact_candidates_dis'.
            with_penetration_keypoints: If True, returns 'penetration_keypoints'.
            with_penetration: If True and scene_pc is provided, returns 'penetration' (signed distances).
            with_distance: If True and scene_pc is provided, returns 'distance' (signed distances).
            with_fingertip_keypoints: If True, returns 'fingertip_keypoints'.

        Returns:
            A dictionary `hand_dict` containing the requested tensors.
        """
        # 任务1.4: 输入预处理 - 检测并记录输入格式信息
        self.set_parameters(hand_pose)

        # 处理scene_pc的维度匹配以适应多抓取格式
        scene_pc_processed = self._expand_input_for_multi_grasp(scene_pc)
        # batch_size = self.batch_size # self.batch_size property is used directly
        hand_dict: Dict[str, torch.Tensor] = {}

        if with_surface_points:
            hand_dict["surface_points"] = self.get_surface_points()

        if with_penetration_keypoints:
            hand_dict["penetration_keypoints"] = self.get_penetration_keypoints()

        if with_contact_candidates:
            if scene_pc_processed is None:
                print("Warning: with_contact_candidates is True but scene_pc is None. Skipping 'contact_candidates_dis'.")
            else:
                if not _PYTORCH3D_AVAILABLE:
                    raise ImportError("PyTorch3D (specifically knn_points) is required for 'contact_candidates_dis'. Please install it.")

                # scene_pc_processed已经经过预处理，确保维度正确
                # 对于多抓取格式，它已经是[B*num_grasps, N, D]
                # 对于单抓取格式，它保持原始格式
                if scene_pc_processed.ndim == 2:
                    if self.hand_pose.shape[0] == 1:  # 使用展平后的批次大小
                        scene_pc_batched = scene_pc_processed.unsqueeze(0)
                    else:
                        raise ValueError(f"scene_pc_processed (shape {scene_pc_processed.shape}) is 2D, but flattened batch_size is {self.hand_pose.shape[0]}. Ambiguous broadcast for contact_candidates_dis.")
                elif scene_pc_processed.ndim == 3:
                    if scene_pc_processed.shape[0] != self.hand_pose.shape[0]:  # 使用展平后的批次大小
                        raise ValueError(f"scene_pc_processed batch_size {scene_pc_processed.shape[0]} does not match flattened batch_size {self.hand_pose.shape[0]} for contact_candidates_dis.")
                    scene_pc_batched = scene_pc_processed
                else:
                    raise ValueError(f"scene_pc_processed must have ndim 2 or 3 for contact_candidates_dis, got {scene_pc_processed.ndim}")

                if scene_pc_batched.shape[-1] < 3:
                    raise ValueError(f"scene_pc must have at least 3 dimensions (xyz) for contact_candidates_dis, got {scene_pc_batched.shape[-1]}")

                # Use only the first three dimensions (x, y, z) for distance calculation
                scene_pc_xyz = scene_pc_batched[..., :3]

                contact_candidates_world = self.get_contact_candidates() # (B_flat, N_candidates, 3)
                if contact_candidates_world.shape[1] == 0:
                    hand_dict["contact_candidates_dis"] = torch.empty(self.hand_pose.shape[0], scene_pc_xyz.shape[1], device=self.device)
                else:
                    scene_pc_xyz, contact_candidates_world = self._ensure_device_consistency(scene_pc_xyz, contact_candidates_world)
                    dists_sq, _, _ = knn_points(scene_pc_xyz, contact_candidates_world, K=1)
                    hand_dict["contact_candidates_dis"] = dists_sq.squeeze(-1)

        if with_meshes:
            all_vertices_world_list = []
            link_names_ordered = list(self.mesh.keys())

            for link_name in link_names_ordered:
                link_mesh_data = self.mesh[link_name]
                link_verts_local = link_mesh_data["vertices"]
                link_verts_transformed = self.current_status[link_name].transform_points(link_verts_local)
                if link_verts_transformed.ndim == 2:
                    link_verts_transformed = link_verts_transformed.unsqueeze(0).expand(self.batch_size, -1, -1)
                link_verts_world = link_verts_transformed @ self.global_rotation.transpose(-2, -1) + self.global_translation.unsqueeze(1)
                all_vertices_world_list.append(link_verts_world)

            if all_vertices_world_list:
                hand_dict["vertices"] = torch.cat(all_vertices_world_list, dim=1)
            else:
                hand_dict["vertices"] = torch.empty(self.batch_size, 0, 3, device=self.device)

            faces_list_for_cat: List[torch.Tensor] = []
            current_n_verts_for_faces = 0
            for link_name in link_names_ordered:
                link_mesh_data = self.mesh[link_name]
                if "faces" in link_mesh_data and link_mesh_data["faces"].numel() > 0:
                    link_faces_local = link_mesh_data["faces"]
                    faces_list_for_cat.append(link_faces_local + current_n_verts_for_faces)
                current_n_verts_for_faces += link_mesh_data["vertices"].shape[0]
            
            if faces_list_for_cat:
                hand_dict["faces"] = torch.cat(faces_list_for_cat, dim=0)
            else:
                hand_dict["faces"] = torch.empty(0, 3, dtype=torch.long, device=self.device)

            # The original code had a call to self.physics.compute_object_penetration here.
            # This was moved to the __call__ method to ensure consistent handling of scene_pc.
            # The penetration_or_distance_values tensor is now managed by the physics component.

        if with_fingertip_keypoints:
            fingertip_points_world_list: List[torch.Tensor] = []
            if not self.fingertip_names:
                print("Warning: with_fingertip_keypoints is True, but self.fingertip_names is empty.")
            
            for link_name in self.fingertip_names:
                if link_name not in self.current_status:
                    print(f"Warning: Fingertip link '{link_name}' not found in current_status. Skipping.")
                    continue
                
                matrix = self.current_status[link_name].get_matrix()
                origin_in_hand_base = matrix[..., :3, 3]

                if origin_in_hand_base.ndim == 1:
                    origin_in_hand_base = origin_in_hand_base.unsqueeze(0).expand(self.batch_size, -1)
                
                origin_world = origin_in_hand_base @ self.global_rotation.transpose(-2, -1) + self.global_translation
                fingertip_points_world_list.append(origin_world.unsqueeze(1))
            
            if fingertip_points_world_list:
                hand_dict["fingertip_keypoints"] = torch.cat(fingertip_points_world_list, dim=1)
            else:
                hand_dict["fingertip_keypoints"] = torch.empty(self.batch_size, 0, 3, device=self.device)
                
        return hand_dict

    def _expand_input_for_multi_grasp(self, input_tensor: Optional[torch.Tensor]) -> Optional[torch.Tensor]:
        """
        Expands a tensor to match the flattened batch size for multi-grasp processing.
        E.g., from (B, N, D) to (B*num_grasps, N, D).
        If not in multi-grasp mode or input is None, the tensor is returned as is.

        Args:
            input_tensor: A tensor to potentially expand, e.g., scene_pc.

        Returns:
            The expanded or original tensor, or None if input was None.
        """
        if not self.is_multi_grasp or input_tensor is None:
            return input_tensor

        if input_tensor.ndim == 2:
            if self.batch_size_original == 1:
                input_batched = input_tensor.unsqueeze(0)
            else:
                raise ValueError(f"Input tensor (shape {input_tensor.shape}) is 2D, but original batch_size is {self.batch_size_original}. Ambiguous broadcast.")
        elif input_tensor.ndim >= 3:
            if input_tensor.shape[0] != self.batch_size_original:
                raise ValueError(f"Input tensor batch_size {input_tensor.shape[0]} does not match original batch_size {self.batch_size_original}.")
            input_batched = input_tensor
        else:
            raise ValueError(f"Input tensor must have ndim 2 or 3, got {input_tensor.ndim}")

        reshape_shape = [-1] + list(input_batched.shape[1:])
        expanded = input_batched.unsqueeze(1).expand(
            self.batch_size_original, self.num_grasps, *input_batched.shape[1:]
        ).reshape(*reshape_shape)
        
        return expanded


if __name__ == "__main__":
    # Initialize hand model
    hand_model = HandModel(
        hand_model_type=HandModelType.LEAP, device='cpu', n_surface_points=1000
    )
    print(hand_model.urdf_path)

